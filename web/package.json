{"name": "deer-flow-web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "dotenv -e ../.env -- next dev --turbo", "scan": "next dev & npx react-scan@latest localhost:3000", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@rc-component/mentions": "^1.2.0", "@t3-oss/env-nextjs": "^0.11.0", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-mention": "^2.12.0", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-text": "^2.12.0", "@tiptap/react": "^2.11.7", "@xyflow/react": "^12.6.0", "best-effort-json-parser": "^1.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "framer-motion": "^12.6.5", "hast": "^1.0.0", "highlight.js": "^11.11.1", "immer": "^10.1.1", "katex": "^0.16.21", "lowlight": "^3.3.0", "lru-cache": "^11.1.0", "lucide-react": "^0.487.0", "motion": "^12.7.4", "nanoid": "^5.1.5", "next": "^15.2.3", "next-themes": "^0.4.6", "novel": "^1.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tippy.js": "^6.3.7", "tiptap-markdown": "^0.8.10", "tw-animate-css": "^1.2.5", "unist-util-visit": "^5.0.0", "use-debounce": "^10.0.4", "use-stick-to-bottom": "^1.1.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.0.15", "@types/hast": "^3.0.4", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-syntax-highlighter": "^15.5.13", "dotenv-cli": "^8.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "raw-loader": "^4.0.2", "tailwindcss": "^4.0.15", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.6.5", "pnpm": {"ignoredBuiltDependencies": ["sharp"]}}