// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { resolveServiceURL } from "./resolve-service-url";

export interface EnhancePromptRequest {
  prompt: string;
  context?: string;
  task_type?: string;
}

export interface PromptAnalysis {
  clarity_score: number;
  specificity_score: number;
  completeness_score: number;
  overall_score: number;
  issues_identified: string[];
  strengths: string[];
}

export interface PromptImprovement {
  category: "clarity" | "specificity" | "completeness" | "structure";
  description: string;
  reasoning: string;
}

export interface EnhancePromptResponse {
  analysis: PromptAnalysis;
  enhanced_prompt: string;
  improvements_made: PromptImprovement[];
  suggestions: string[];
}

export async function enhancePrompt(request: EnhancePromptRequest): Promise<EnhancePromptResponse> {
  const response = await fetch(resolveServiceURL("prompt/enhance"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  
  // Parse the result if it's a JSON string
  let result = data.result;
  if (typeof result === 'string') {
    try {
      result = JSON.parse(result);
    } catch (e) {
      // If parsing fails, create a fallback response
      result = {
        analysis: {
          clarity_score: 7,
          specificity_score: 6,
          completeness_score: 6,
          overall_score: 6,
          issues_identified: ["Could be more specific"],
          strengths: ["Clear intent"]
        },
        enhanced_prompt: request.prompt,
        improvements_made: [],
        suggestions: ["Consider adding more specific details about your requirements"]
      };
    }
  }

  return result;
}
