// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { AnimatePresence, motion } from "framer-motion";
import { ArrowUp, X } from "lucide-react";
import { useCallback, useRef, useState } from "react";

import { Detective } from "~/components/deer-flow/icons/detective";
import { Enhance } from "~/components/deer-flow/icons/enhance";
import MessageInput, {
  type MessageInputRef,
} from "~/components/deer-flow/message-input";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Button } from "~/components/ui/button";
import type { Option, Resource } from "~/core/messages";
import {
  setEnableBackgroundInvestigation,
  useSettingsStore,
} from "~/core/store";
import { cn } from "~/lib/utils";
import { enhancePrompt, type EnhancePromptResponse } from "~/core/api";

export function InputBox({
  className,
  responding,
  feedback,
  onSend,
  onCancel,
  onRemoveFeedback,
}: {
  className?: string;
  size?: "large" | "normal";
  responding?: boolean;
  feedback?: { option: Option } | null;
  onSend?: (
    message: string,
    options?: {
      interruptFeedback?: string;
      resources?: Array<Resource>;
    },
  ) => void;
  onCancel?: () => void;
  onRemoveFeedback?: () => void;
}) {
  const backgroundInvestigation = useSettingsStore(
    (state) => state.general.enableBackgroundInvestigation,
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<MessageInputRef>(null);
  const feedbackRef = useRef<HTMLDivElement>(null);

  // Enhancement state
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementSuggestion, setEnhancementSuggestion] = useState<EnhancePromptResponse | null>(null);
  const [currentPrompt, setCurrentPrompt] = useState("");

  const handleSendMessage = useCallback(
    (message: string, resources: Array<Resource>) => {
      if (responding) {
        onCancel?.();
      } else {
        if (message.trim() === "") {
          return;
        }
        if (onSend) {
          onSend(message, {
            interruptFeedback: feedback?.option.value,
            resources,
          });
          onRemoveFeedback?.();
          // Clear enhancement suggestion after sending
          setEnhancementSuggestion(null);
        }
      }
    },
    [responding, onCancel, onSend, feedback, onRemoveFeedback],
  );

  const handleEnhancePrompt = useCallback(async () => {
    if (currentPrompt.trim() === "" || isEnhancing) {
      return;
    }

    setIsEnhancing(true);
    try {
      const response = await enhancePrompt({
        prompt: currentPrompt,
        task_type: "general"
      });
      setEnhancementSuggestion(response);
    } catch (error) {
      console.error("Failed to enhance prompt:", error);
      // Could add toast notification here
    } finally {
      setIsEnhancing(false);
    }
  }, [currentPrompt, isEnhancing]);

  const handleAcceptSuggestion = useCallback(() => {
    if (enhancementSuggestion && inputRef.current) {
      // Update the input with the enhanced prompt
      inputRef.current.setContent(enhancementSuggestion.enhanced_prompt);
      setCurrentPrompt(enhancementSuggestion.enhanced_prompt);
      setEnhancementSuggestion(null);
    }
  }, [enhancementSuggestion]);

  const handleRejectSuggestion = useCallback(() => {
    setEnhancementSuggestion(null);
  }, []);

  return (
    <div
      className={cn(
        "bg-card relative flex h-full w-full flex-col rounded-[24px] border",
        className,
      )}
      ref={containerRef}
    >
      <div className="w-full">
        <AnimatePresence>
          {feedback && (
            <motion.div
              ref={feedbackRef}
              className="bg-background border-brand absolute top-0 left-0 mt-2 ml-4 flex items-center justify-center gap-1 rounded-2xl border px-2 py-0.5"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
            >
              <div className="text-brand flex h-full w-full items-center justify-center text-sm opacity-90">
                {feedback.option.text}
              </div>
              <X
                className="cursor-pointer opacity-60"
                size={16}
                onClick={onRemoveFeedback}
              />
            </motion.div>
          )}
          {enhancementSuggestion && (
            <motion.div
              className="bg-background border-blue-500 absolute top-0 left-0 right-0 mt-2 mx-4 rounded-2xl border p-4 shadow-lg"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Enhance className="text-blue-500" size={16} />
                  <span className="text-blue-500 font-medium text-sm">Enhanced Prompt Suggestion</span>
                </div>
                <X
                  className="cursor-pointer opacity-60 hover:opacity-100"
                  size={16}
                  onClick={handleRejectSuggestion}
                />
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-3">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {enhancementSuggestion.enhanced_prompt}
                </p>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <span>Score: {enhancementSuggestion.analysis.overall_score}/10</span>
                  <span>•</span>
                  <span>{enhancementSuggestion.improvements_made.length} improvements</span>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleRejectSuggestion}
                    className="text-xs"
                  >
                    Keep Original
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleAcceptSuggestion}
                    className="text-xs bg-blue-500 hover:bg-blue-600"
                  >
                    Use Enhanced
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        <MessageInput
          className={cn("h-24 px-4 pt-5", feedback && "pt-9", enhancementSuggestion && "pt-20")}
          ref={inputRef}
          onEnter={handleSendMessage}
          onChange={setCurrentPrompt}
        />
      </div>
      <div className="flex items-center px-4 py-2">
        <div className="flex grow gap-2">
          <Tooltip
            className="max-w-60"
            title={
              <div>
                <h3 className="mb-2 font-bold">
                  Investigation Mode: {backgroundInvestigation ? "On" : "Off"}
                </h3>
                <p>
                  When enabled, DeerFlow will perform a quick search before
                  planning. This is useful for researches related to ongoing
                  events and news.
                </p>
              </div>
            }
          >
            <Button
              className={cn(
                "rounded-2xl",
                backgroundInvestigation && "!border-brand !text-brand",
              )}
              variant="outline"
              onClick={() =>
                setEnableBackgroundInvestigation(!backgroundInvestigation)
              }
            >
              <Detective /> Investigation
            </Button>
          </Tooltip>
          <Tooltip
            className="max-w-60"
            title={
              <div>
                <h3 className="mb-2 font-bold">Enhance Prompt</h3>
                <p>
                  Analyze and improve your prompt to make it more effective and specific.
                  Get suggestions for clarity, completeness, and better results.
                </p>
              </div>
            }
          >
            <Button
              className="rounded-2xl"
              variant="outline"
              onClick={handleEnhancePrompt}
              disabled={isEnhancing || currentPrompt.trim() === ""}
            >
              <Enhance /> {isEnhancing ? "Enhancing..." : "Enhance"}
            </Button>
          </Tooltip>
        </div>
        <div className="flex shrink-0 items-center gap-2">
          <Tooltip title={responding ? "Stop" : "Send"}>
            <Button
              variant="outline"
              size="icon"
              className={cn("h-10 w-10 rounded-full")}
              onClick={() => inputRef.current?.submit()}
            >
              {responding ? (
                <div className="flex h-10 w-10 items-center justify-center">
                  <div className="bg-foreground h-4 w-4 rounded-sm opacity-70" />
                </div>
              ) : (
                <ArrowUp />
              )}
            </Button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
}
