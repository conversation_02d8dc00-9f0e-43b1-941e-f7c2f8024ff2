# 🦌 DeerFlow

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![DeepWiki](https://img.shields.io/badge/DeepWiki-bytedance%2Fdeer--flow-blue.svg?logo=data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAyCAYAAAAnWDnqAAAAAXNSR0IArs4c6QAAA05JREFUaEPtmUtyEzEQhtWTQyQLHNak2AB7ZnyXZMEjXMGeK/AIi+QuHrMnbChYY7MIh8g01fJoopFb0uhhEqqcbWTp06/uv1saEDv4O3n3dV60RfP947Mm9/SQc0ICFQgzfc4CYZoTPAswgSJCCUJUnAAoRHOAUOcATwbmVLWdGoH//PB8mnKqScAhsD0kYP3j/Yt5LPQe2KvcXmGvRHcDnpxfL2zOYJ1mFwrryWTz0advv1Ut4CJgf5uhDuDj5eUcAUoahrdY/56ebRWeraTjMt/00Sh3UDtjgHtQNHwcRGOC98BJEAEymycmYcWwOprTgcB6VZ5JK5TAJ+fXGLBm3FDAmn6oPPjR4rKCAoJCal2eAiQp2x0vxTPB3ALO2CRkwmDy5WohzBDwSEFKRwPbknEggCPB/imwrycgxX2NzoMCHhPkDwqYMr9tRcP5qNrMZHkVnOjRMWwLCcr8ohBVb1OMjxLwGCvjTikrsBOiA6fNyCrm8V1rP93iVPpwaE+gO0SsWmPiXB+jikdf6SizrT5qKasx5j8ABbHpFTx+vFXp9EnYQmLx02h1QTTrl6eDqxLnGjporxl3NL3agEvXdT0WmEost648sQOYAeJS9Q7bfUVoMGnjo4AZdUMQku50McCcMWcBPvr0SzbTAFDfvJqwLzgxwATnCgnp4wDl6Aa+Ax283gghmj+vj7feE2KBBRMW3FzOpLOADl0Isb5587h/U4gGvkt5v60Z1VLG8BhYjbzRwyQZemwAd6cCR5/XFWLYZRIMpX39AR0tjaGGiGzLVyhse5C9RKC6ai42ppWPKiBagOvaYk8lO7DajerabOZP46Lby5wKjw1HCRx7p9sVMOWGzb/vA1hwiWc6jm3MvQDTogQkiqIhJV0nBQBTU+3okKCFDy9WwferkHjtxib7t3xIUQtHxnIwtx4mpg26/HfwVNVDb4oI9RHmx5WGelRVlrtiw43zboCLaxv46AZeB3IlTkwouebTr1y2NjSpHz68WNFjHvupy3q8TFn3Hos2IAk4Ju5dCo8B3wP7VPr/FGaKiG+T+v+TQqIrOqMTL1VdWV1DdmcbO8KXBz6esmYWYKPwDL5b5FA1a0hwapHiom0r/cKaoqr+27/XcrS5UwSMbQAAAABJRU5ErkJggg==)](https://deepwiki.com/bytedance/deer-flow)
<!-- DeepWiki badge generated by https://deepwiki.ryoppippi.com/ -->

[English](./README.md) | [简体中文](./README_zh.md) | [日本語](./README_ja.md) | [Deutsch](./README_de.md) | [Español](./README_es.md) | [Русский](./README_ru.md) | [Portuguese](./README_pt.md)

> Aus Open Source entstanden, an Open Source zurückgeben.

**DeerFlow** (**D**eep **E**xploration and **E**fficient **R**esearch **Flow**) ist ein Community-getriebenes Framework für tiefgehende Recherche, das auf der großartigen Arbeit der Open-Source-Community aufbaut. Unser Ziel ist es, Sprachmodelle mit spezialisierten Werkzeugen für Aufgaben wie Websuche, Crawling und Python-Code-Ausführung zu kombinieren und gleichzeitig der Community, die dies möglich gemacht hat, etwas zurückzugeben.

Besuchen Sie [unsere offizielle Website](https://deerflow.tech/) für weitere Details.

## Demo

### Video

https://github.com/user-attachments/assets/f3786598-1f2a-4d07-919e-8b99dfa1de3e

In dieser Demo zeigen wir, wie man DeerFlow nutzt, um:
- Nahtlos mit MCP-Diensten zu integrieren
- Den Prozess der tiefgehenden Recherche durchzuführen und einen umfassenden Bericht mit Bildern zu erstellen
- Podcast-Audio basierend auf dem generierten Bericht zu erstellen

### Wiedergaben

- [Wie hoch ist der Eiffelturm im Vergleich zum höchsten Gebäude?](https://deerflow.tech/chat?replay=eiffel-tower-vs-tallest-building)
- [Was sind die angesagtesten Repositories auf GitHub?](https://deerflow.tech/chat?replay=github-top-trending-repo)
- [Einen Artikel über traditionelle Gerichte aus Nanjing schreiben](https://deerflow.tech/chat?replay=nanjing-traditional-dishes)
- [Wie dekoriert man eine Mietwohnung?](https://deerflow.tech/chat?replay=rental-apartment-decoration)
- [Besuchen Sie unsere offizielle Website, um weitere Wiedergaben zu entdecken.](https://deerflow.tech/#case-studies)

---


## 📑 Inhaltsverzeichnis

- [🚀 Schnellstart](#schnellstart)
- [🌟 Funktionen](#funktionen)
- [🏗️ Architektur](#architektur)
- [🛠️ Entwicklung](#entwicklung)
- [🗣️ Text-zu-Sprache-Integration](#text-zu-sprache-integration)
- [📚 Beispiele](#beispiele)
- [❓ FAQ](#faq)
- [📜 Lizenz](#lizenz)
- [💖 Danksagungen](#danksagungen)
- [⭐ Star-Verlauf](#star-verlauf)


## Schnellstart

DeerFlow ist in Python entwickelt und kommt mit einer in Node.js geschriebenen Web-UI. Um einen reibungslosen Einrichtungsprozess zu gewährleisten, empfehlen wir die Verwendung der folgenden Tools:

### Empfohlene Tools
- **[`uv`](https://docs.astral.sh/uv/getting-started/installation/):**
  Vereinfacht die Verwaltung von Python-Umgebungen und Abhängigkeiten. `uv` erstellt automatisch eine virtuelle Umgebung im Stammverzeichnis und installiert alle erforderlichen Pakete für Sie—keine manuelle Installation von Python-Umgebungen notwendig.

- **[`nvm`](https://github.com/nvm-sh/nvm):**
  Verwalten Sie mühelos mehrere Versionen der Node.js-Laufzeit.

- **[`pnpm`](https://pnpm.io/installation):**
  Installieren und verwalten Sie Abhängigkeiten des Node.js-Projekts.

### Umgebungsanforderungen
Stellen Sie sicher, dass Ihr System die folgenden Mindestanforderungen erfüllt:
- **[Python](https://www.python.org/downloads/):** Version `3.12+`
- **[Node.js](https://nodejs.org/en/download/):** Version `22+`

### Installation
```bash
# Repository klonen
git clone https://github.com/bytedance/deer-flow.git
cd deer-flow

# Abhängigkeiten installieren, uv kümmert sich um den Python-Interpreter und die Erstellung der venv sowie die Installation der erforderlichen Pakete
uv sync

# Konfigurieren Sie .env mit Ihren API-Schlüsseln
# Tavily: https://app.tavily.com/home
# Brave_SEARCH: https://brave.com/search/api/
# volcengine TTS: Fügen Sie Ihre TTS-Anmeldedaten hinzu, falls vorhanden
cp .env.example .env

# Siehe die Abschnitte 'Unterstützte Suchmaschinen' und 'Text-zu-Sprache-Integration' unten für alle verfügbaren Optionen

# Konfigurieren Sie conf.yaml für Ihr LLM-Modell und API-Schlüssel
# Weitere Details finden Sie unter 'docs/configuration_guide.md'
cp conf.yaml.example conf.yaml

# Installieren Sie marp für PPT-Generierung
# https://github.com/marp-team/marp-cli?tab=readme-ov-file#use-package-manager
brew install marp-cli
```

Optional können Sie Web-UI-Abhängigkeiten über [pnpm](https://pnpm.io/installation) installieren:

```bash
cd deer-flow/web
pnpm install
```

### Konfigurationen

Weitere Informationen finden Sie im [Konfigurationsleitfaden](docs/configuration_guide.md).

> [!HINWEIS]
> Lesen Sie den Leitfaden sorgfältig, bevor Sie das Projekt starten, und aktualisieren Sie die Konfigurationen entsprechend Ihren spezifischen Einstellungen und Anforderungen.

### Konsolen-UI

Der schnellste Weg, um das Projekt auszuführen, ist die Verwendung der Konsolen-UI.

```bash
# Führen Sie das Projekt in einer bash-ähnlichen Shell aus
uv run main.py
```

### Web-UI

Dieses Projekt enthält auch eine Web-UI, die ein dynamischeres und ansprechenderes interaktives Erlebnis bietet.
> [!HINWEIS]
> Sie müssen zuerst die Abhängigkeiten der Web-UI installieren.

```bash
# Führen Sie sowohl den Backend- als auch den Frontend-Server im Entwicklungsmodus aus
# Unter macOS/Linux
./bootstrap.sh -d

# Unter Windows
bootstrap.bat -d
```

Öffnen Sie Ihren Browser und besuchen Sie [`http://localhost:3000`](http://localhost:3000), um die Web-UI zu erkunden.

Weitere Details finden Sie im Verzeichnis [`web`](./web/).


## Unterstützte Suchmaschinen

DeerFlow unterstützt mehrere Suchmaschinen, die in Ihrer `.env`-Datei über die Variable `SEARCH_API` konfiguriert werden können:

- **Tavily** (Standard): Eine spezialisierte Such-API für KI-Anwendungen
    - Erfordert `TAVILY_API_KEY` in Ihrer `.env`-Datei
    - Registrieren Sie sich unter: https://app.tavily.com/home

- **DuckDuckGo**: Datenschutzorientierte Suchmaschine
    - Kein API-Schlüssel erforderlich

- **Brave Search**: Datenschutzorientierte Suchmaschine mit erweiterten Funktionen
    - Erfordert `BRAVE_SEARCH_API_KEY` in Ihrer `.env`-Datei
    - Registrieren Sie sich unter: https://brave.com/search/api/

- **Arxiv**: Wissenschaftliche Papiersuche für akademische Forschung
    - Kein API-Schlüssel erforderlich
    - Spezialisiert auf wissenschaftliche und akademische Papiere

Um Ihre bevorzugte Suchmaschine zu konfigurieren, setzen Sie die Variable `SEARCH_API` in Ihrer `.env`-Datei:

```bash
# Wählen Sie eine: tavily, duckduckgo, brave_search, arxiv
SEARCH_API=tavily
```

## Funktionen

### Kernfähigkeiten

- 🤖 **LLM-Integration**
    - Unterstützt die Integration der meisten Modelle über [litellm](https://docs.litellm.ai/docs/providers).
    - Unterstützung für Open-Source-Modelle wie Qwen
    - OpenAI-kompatible API-Schnittstelle
    - Mehrstufiges LLM-System für unterschiedliche Aufgabenkomplexitäten

### Tools und MCP-Integrationen

- 🔍 **Suche und Abruf**
    - Websuche über Tavily, Brave Search und mehr
    - Crawling mit Jina
    - Fortgeschrittene Inhaltsextraktion

- 🔗 **MCP Nahtlose Integration**
    - Erweiterte Fähigkeiten für privaten Domänenzugriff, Wissensgraphen, Webbrowsing und mehr
    - Erleichtert die Integration verschiedener Forschungswerkzeuge und -methoden

### Menschliche Zusammenarbeit

- 🧠 **Mensch-in-der-Schleife**
    - Unterstützt interaktive Modifikation von Forschungsplänen mit natürlicher Sprache
    - Unterstützt automatische Akzeptanz von Forschungsplänen

- 📝 **Bericht-Nachbearbeitung**
    - Unterstützt Notion-ähnliche Blockbearbeitung
    - Ermöglicht KI-Verfeinerungen, einschließlich KI-unterstützter Polierung, Satzkürzung und -erweiterung
    - Angetrieben von [tiptap](https://tiptap.dev/)

### Inhaltserstellung

- 🎙️ **Podcast- und Präsentationserstellung**
    - KI-gestützte Podcast-Skripterstellung und Audiosynthese
    - Automatisierte Erstellung einfacher PowerPoint-Präsentationen
    - Anpassbare Vorlagen für maßgeschneiderte Inhalte


## Architektur

DeerFlow implementiert eine modulare Multi-Agenten-Systemarchitektur, die für automatisierte Forschung und Codeanalyse konzipiert ist. Das System basiert auf LangGraph und ermöglicht einen flexiblen zustandsbasierten Workflow, bei dem Komponenten über ein klar definiertes Nachrichtenübermittlungssystem kommunizieren.

![Architekturdiagramm](./assets/architecture.png)
> Sehen Sie es live auf [deerflow.tech](https://deerflow.tech/#multi-agent-architecture)

Das System verwendet einen optimierten Workflow mit den folgenden Komponenten:

1. **Koordinator**: Der Einstiegspunkt, der den Workflow-Lebenszyklus verwaltet
   - Initiiert den Forschungsprozess basierend auf Benutzereingaben
   - Delegiert Aufgaben bei Bedarf an den Planer
   - Fungiert als primäre Schnittstelle zwischen dem Benutzer und dem System

2. **Planer**: Strategische Komponente für Aufgabenzerlegung und -planung
   - Analysiert Forschungsziele und erstellt strukturierte Ausführungspläne
   - Bestimmt, ob ausreichend Kontext verfügbar ist oder ob weitere Forschung benötigt wird
   - Verwaltet den Forschungsablauf und entscheidet, wann der endgültige Bericht erstellt wird

3. **Forschungsteam**: Eine Sammlung spezialisierter Agenten, die den Plan ausführen:
   - **Forscher**: Führt Websuchen und Informationssammlung mit Tools wie Websuchmaschinen, Crawling und sogar MCP-Diensten durch.
   - **Codierer**: Behandelt Codeanalyse, -ausführung und technische Aufgaben mit dem Python REPL Tool.
   Jeder Agent hat Zugriff auf spezifische Tools, die für seine Rolle optimiert sind, und operiert innerhalb des LangGraph-Frameworks

4. **Reporter**: Endphasenprozessor für Forschungsergebnisse
   - Aggregiert Erkenntnisse vom Forschungsteam
   - Verarbeitet und strukturiert die gesammelten Informationen
   - Erstellt umfassende Forschungsberichte

## Text-zu-Sprache-Integration

DeerFlow enthält jetzt eine Text-zu-Sprache (TTS)-Funktion, mit der Sie Forschungsberichte in Sprache umwandeln können. Diese Funktion verwendet die volcengine TTS API, um hochwertige Audios aus Text zu generieren. Funktionen wie Geschwindigkeit, Lautstärke und Tonhöhe können ebenfalls angepasst werden.

### Verwendung der TTS API

Sie können auf die TTS-Funktionalität über den Endpunkt `/api/tts` zugreifen:

```bash
# Beispiel API-Aufruf mit curl
curl --location 'http://localhost:8000/api/tts' \
--header 'Content-Type: application/json' \
--data '{
    "text": "Dies ist ein Test der Text-zu-Sprache-Funktionalität.",
    "speed_ratio": 1.0,
    "volume_ratio": 1.0,
    "pitch_ratio": 1.0
}' \
--output speech.mp3
```


## Entwicklung

### Testen

Führen Sie die Testsuite aus:

```bash
# Alle Tests ausführen
make test

# Spezifische Testdatei ausführen
pytest tests/integration/test_workflow.py

# Mit Abdeckung ausführen
make coverage
```

### Codequalität

```bash
# Lint ausführen
make lint

# Code formatieren
make format
```

### Debugging mit LangGraph Studio

DeerFlow verwendet LangGraph für seine Workflow-Architektur. Sie können LangGraph Studio verwenden, um den Workflow in Echtzeit zu debuggen und zu visualisieren.

#### LangGraph Studio lokal ausführen

DeerFlow enthält eine `langgraph.json`-Konfigurationsdatei, die die Graphstruktur und Abhängigkeiten für das LangGraph Studio definiert. Diese Datei verweist auf die im Projekt definierten Workflow-Graphen und lädt automatisch Umgebungsvariablen aus der `.env`-Datei.

##### Mac

```bash
# Installieren Sie den uv-Paketmanager, wenn Sie ihn noch nicht haben
curl -LsSf https://astral.sh/uv/install.sh | sh

# Installieren Sie Abhängigkeiten und starten Sie den LangGraph-Server
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.12 langgraph dev --allow-blocking
```

##### Windows / Linux

```bash
# Abhängigkeiten installieren
pip install -e .
pip install -U "langgraph-cli[inmem]"

# LangGraph-Server starten
langgraph dev
```

Nach dem Start des LangGraph-Servers sehen Sie mehrere URLs im Terminal:
- API: http://127.0.0.1:2024
- Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
- API-Dokumentation: http://127.0.0.1:2024/docs

Öffnen Sie den Studio UI-Link in Ihrem Browser, um auf die Debugging-Schnittstelle zuzugreifen.

#### Verwendung von LangGraph Studio

In der Studio UI können Sie:

1. Den Workflow-Graphen visualisieren und sehen, wie Komponenten verbunden sind
2. Die Ausführung in Echtzeit verfolgen, um zu sehen, wie Daten durch das System fließen
3. Den Zustand in jedem Schritt des Workflows inspizieren
4. Probleme durch Untersuchung von Ein- und Ausgaben jeder Komponente debuggen
5. Feedback während der Planungsphase geben, um Forschungspläne zu verfeinern

Wenn Sie ein Forschungsthema in der Studio UI einreichen, können Sie die gesamte Workflow-Ausführung sehen, einschließlich:
- Die Planungsphase, in der der Forschungsplan erstellt wird
- Die Feedback-Schleife, in der Sie den Plan ändern können
- Die Forschungs- und Schreibphasen für jeden Abschnitt
- Die Erstellung des endgültigen Berichts

### Aktivieren von LangSmith-Tracing

DeerFlow unterstützt LangSmith-Tracing, um Ihnen beim Debuggen und Überwachen Ihrer Workflows zu helfen. Um LangSmith-Tracing zu aktivieren:

1. Stellen Sie sicher, dass Ihre `.env`-Datei die folgenden Konfigurationen enthält (siehe `.env.example`):
   ```bash
   LANGSMITH_TRACING=true
   LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
   LANGSMITH_API_KEY="xxx"
   LANGSMITH_PROJECT="xxx"
   ```

2. Starten Sie das Tracing mit LangSmith lokal, indem Sie folgenden Befehl ausführen:
   ```bash
   langgraph dev
   ```

Dies aktiviert die Trace-Visualisierung in LangGraph Studio und sendet Ihre Traces zur Überwachung und Analyse an LangSmith.

## Beispiele

Die folgenden Beispiele demonstrieren die Fähigkeiten von DeerFlow:

### Forschungsberichte

1. **OpenAI Sora Bericht** - Analyse von OpenAIs Sora KI-Tool
   - Diskutiert Funktionen, Zugang, Prompt-Engineering, Einschränkungen und ethische Überlegungen
   - [Vollständigen Bericht ansehen](examples/openai_sora_report.md)

2. **Googles Agent-to-Agent-Protokoll Bericht** - Überblick über Googles Agent-to-Agent (A2A)-Protokoll
   - Diskutiert seine Rolle in der KI-Agentenkommunikation und seine Beziehung zum Model Context Protocol (MCP) von Anthropic
   - [Vollständigen Bericht ansehen](examples/what_is_agent_to_agent_protocol.md)

3. **Was ist MCP?** - Eine umfassende Analyse des Begriffs "MCP" in mehreren Kontexten
   - Untersucht Model Context Protocol in KI, Monocalciumphosphat in der Chemie und Micro-channel Plate in der Elektronik
   - [Vollständigen Bericht ansehen](examples/what_is_mcp.md)

4. **Bitcoin-Preisschwankungen** - Analyse der jüngsten Bitcoin-Preisbewegungen
   - Untersucht Markttrends, regulatorische Einflüsse und technische Indikatoren
   - Bietet Empfehlungen basierend auf historischen Daten
   - [Vollständigen Bericht ansehen](examples/bitcoin_price_fluctuation.md)

5. **Was ist LLM?** - Eine eingehende Erforschung großer Sprachmodelle
   - Diskutiert Architektur, Training, Anwendungen und ethische Überlegungen
   - [Vollständigen Bericht ansehen](examples/what_is_llm.md)

6. **Wie nutzt man Claude für tiefgehende Recherche?** - Best Practices und Workflows für die Verwendung von Claude in der tiefgehenden Forschung
   - Behandelt Prompt-Engineering, Datenanalyse und Integration mit anderen Tools
   - [Vollständigen Bericht ansehen](examples/how_to_use_claude_deep_research.md)

7. **KI-Adoption im Gesundheitswesen: Einflussfaktoren** - Analyse der Faktoren, die die KI-Adoption im Gesundheitswesen vorantreiben
   - Diskutiert KI-Technologien, Datenqualität, ethische Überlegungen, wirtschaftliche Bewertungen, organisatorische Bereitschaft und digitale Infrastruktur
   - [Vollständigen Bericht ansehen](examples/AI_adoption_in_healthcare.md)

8. **Auswirkungen des Quantencomputing auf die Kryptographie** - Analyse der Auswirkungen des Quantencomputing auf die Kryptographie
   - Diskutiert Schwachstellen der klassischen Kryptographie, Post-Quanten-Kryptographie und quantenresistente kryptographische Lösungen
   - [Vollständigen Bericht ansehen](examples/Quantum_Computing_Impact_on_Cryptography.md)

9. **Cristiano Ronaldos Leistungshöhepunkte** - Analyse der Leistungshöhepunkte von Cristiano Ronaldo
   - Diskutiert seine Karriereerfolge, internationalen Tore und Leistungen in verschiedenen Spielen
   - [Vollständigen Bericht ansehen](examples/Cristiano_Ronaldo's_Performance_Highlights.md)

Um diese Beispiele auszuführen oder Ihre eigenen Forschungsberichte zu erstellen, können Sie die folgenden Befehle verwenden:

```bash
# Mit einer spezifischen Anfrage ausführen
uv run main.py "Welche Faktoren beeinflussen die KI-Adoption im Gesundheitswesen?"

# Mit benutzerdefinierten Planungsparametern ausführen
uv run main.py --max_plan_iterations 3 "Wie wirkt sich Quantencomputing auf die Kryptographie aus?"

# Im interaktiven Modus mit eingebauten Fragen ausführen
uv run main.py --interactive

# Oder mit grundlegendem interaktiven Prompt ausführen
uv run main.py

# Alle verfügbaren Optionen anzeigen
uv run main.py --help
```

### Interaktiver Modus

Die Anwendung unterstützt jetzt einen interaktiven Modus mit eingebauten Fragen in Englisch und Chinesisch:

1. Starten Sie den interaktiven Modus:
   ```bash
   uv run main.py --interactive
   ```

2. Wählen Sie Ihre bevorzugte Sprache (English oder 中文)

3. Wählen Sie aus einer Liste von eingebauten Fragen oder wählen Sie die Option, Ihre eigene Frage zu stellen

4. Das System wird Ihre Frage verarbeiten und einen umfassenden Forschungsbericht generieren

### Mensch-in-der-Schleife

DeerFlow enthält einen Mensch-in-der-Schleife-Mechanismus, der es Ihnen ermöglicht, Forschungspläne vor ihrer Ausführung zu überprüfen, zu bearbeiten und zu genehmigen:

1. **Planüberprüfung**: Wenn Mensch-in-der-Schleife aktiviert ist, präsentiert das System den generierten Forschungsplan zur Überprüfung vor der Ausführung

2. **Feedback geben**: Sie können:
   - Den Plan akzeptieren, indem Sie mit `[ACCEPTED]` antworten
   - Den Plan bearbeiten, indem Sie Feedback geben (z.B., `[EDIT PLAN] Fügen Sie mehr Schritte zur technischen Implementierung hinzu`)
   - Das System wird Ihr Feedback einarbeiten und einen überarbeiteten Plan generieren

3. **Automatische Akzeptanz**: Sie können die automatische Akzeptanz aktivieren, um den Überprüfungsprozess zu überspringen:
   - Über API: Setzen Sie `auto_accepted_plan: true` in Ihrer Anfrage

4. **API-Integration**: Bei Verwendung der API können Sie Feedback über den Parameter `feedback` geben:
   ```json
   {
     "messages": [{"role": "user", "content": "Was ist Quantencomputing?"}],
     "thread_id": "my_thread_id",
     "auto_accepted_plan": false,
     "feedback": "[EDIT PLAN] Mehr über Quantenalgorithmen aufnehmen"
   }
   ```

### Kommandozeilenargumente

Die Anwendung unterstützt mehrere Kommandozeilenargumente, um ihr Verhalten anzupassen:

- **query**: Die zu verarbeitende Forschungsanfrage (kann mehrere Wörter umfassen)
- **--interactive**: Im interaktiven Modus mit eingebauten Fragen ausführen
- **--max_plan_iterations**: Maximale Anzahl von Planungszyklen (Standard: 1)
- **--max_step_num**: Maximale Anzahl von Schritten in einem Forschungsplan (Standard: 3)
- **--debug**: Detaillierte Debug-Protokollierung aktivieren

## FAQ

Weitere Informationen finden Sie in der [FAQ.md](docs/FAQ.md).

## Lizenz

Dieses Projekt ist Open Source und unter der [MIT-Lizenz](./LICENSE) verfügbar.

## Danksagungen

DeerFlow baut auf der unglaublichen Arbeit der Open-Source-Community auf. Wir sind allen Projekten und Mitwirkenden zutiefst dankbar, deren Bemühungen DeerFlow möglich gemacht haben. Wahrhaftig stehen wir auf den Schultern von Riesen.

Wir möchten unsere aufrichtige Wertschätzung den folgenden Projekten für ihre unschätzbaren Beiträge aussprechen:

- **[LangChain](https://github.com/langchain-ai/langchain)**: Ihr außergewöhnliches Framework unterstützt unsere LLM-Interaktionen und -Ketten und ermöglicht nahtlose Integration und Funktionalität.
- **[LangGraph](https://github.com/langchain-ai/langgraph)**: Ihr innovativer Ansatz zur Multi-Agenten-Orchestrierung war maßgeblich für die Ermöglichung der ausgeklügelten Workflows von DeerFlow.

Diese Projekte veranschaulichen die transformative Kraft der Open-Source-Zusammenarbeit, und wir sind stolz darauf, auf ihren Grundlagen aufzubauen.

### Hauptmitwirkende
Ein herzliches Dankeschön geht an die Hauptautoren von `DeerFlow`, deren Vision, Leidenschaft und Engagement dieses Projekt zum Leben erweckt haben:

- **[Daniel Walnut](https://github.com/hetaoBackend/)**
- **[Henry Li](https://github.com/magiccube/)**

Ihr unerschütterliches Engagement und Fachwissen waren die treibende Kraft hinter dem Erfolg von DeerFlow. Wir fühlen uns geehrt, Sie an der Spitze dieser Reise zu haben.

## Star-Verlauf

[![Star History Chart](https://api.star-history.com/svg?repos=bytedance/deer-flow&type=Date)](https://star-history.com/#bytedance/deer-flow&Date) 