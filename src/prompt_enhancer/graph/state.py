# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import TypedDict, Optional


class PromptEnhancerState(TypedDict):
    """State for the prompt enhancer workflow."""
    
    prompt: str  # Original prompt to enhance
    context: Optional[str]  # Additional context
    task_type: Optional[str]  # Type of task (research, writing, analysis, etc.)
    output: Optional[str]  # Enhanced prompt result
