# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging

from langchain.schema import HumanMessage, SystemMessage

from src.config.agents import AGENT_LLM_MAP
from src.llms.llm import get_llm_by_type
from src.prompts.template import get_prompt_template
from src.prompt_enhancer.graph.state import PromptEnhancerState

logger = logging.getLogger(__name__)


def prompt_enhancer_node(state: PromptEnhancerState):
    """Node that enhances user prompts using AI analysis."""
    logger.info("Enhancing user prompt...")
    
    model = get_llm_by_type(AGENT_LLM_MAP["basic"])
    
    # Prepare the prompt template variables
    template_vars = {
        "prompt": state["prompt"],
        "context": state.get("context", ""),
        "task_type": state.get("task_type", "general")
    }
    
    try:
        # Replace the system message with our enhanced template
        
        system_prompt = get_prompt_template("prompt_enhancer/prompt_enhancer")
        
        # Render the template with variables
        from jinja2 import Template
        template = Template(system_prompt)
        rendered_prompt = template.render(**template_vars)
        
        # Create messages
        messages = [
            SystemMessage(content=rendered_prompt),
            HumanMessage(content="Please analyze and enhance this prompt.")
        ]
        
        # Get the response from the model
        response = model.invoke(messages)
        
        logger.info("Prompt enhancement completed successfully")
        return {"output": response.content}
        
    except Exception as e:
        logger.error(f"Error in prompt enhancement: {str(e)}")
        # Return a fallback response
        fallback_response = {
            "analysis": {
                "clarity_score": 7,
                "specificity_score": 6,
                "completeness_score": 6,
                "overall_score": 6,
                "issues_identified": ["Could be more specific"],
                "strengths": ["Clear intent"]
            },
            "enhanced_prompt": state["prompt"],
            "improvements_made": [],
            "suggestions": ["Consider adding more specific details about your requirements"]
        }
        return {"output": json.dumps(fallback_response)}
